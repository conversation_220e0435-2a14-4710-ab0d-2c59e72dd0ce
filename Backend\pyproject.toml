[build-system]
requires = ["setuptools>=42"]
build-backend = "setuptools.build_meta"

[project]
name = "flight_backend"
version = "0.1.0"
description = "Flight Booking Portal Backend"
requires-python = ">=3.8"
dependencies = [
    "quart>=0.17.0",
    "aiohttp>=3.8.0",
    "python-dotenv>=0.19.0",
    "quart-cors>=0.3.0",
    "pydantic>=1.8.0"
]

[tool.setuptools.packages.find]
where = ["."]
include = ["Backend*"]
