# Core dependencies for Quart backend
quart>=0.18.0
quart-cors>=0.0.4
python-dotenv>=1.0.0
pyjwt>=2.8.0
requests>=2.31.0
python-multipart>=0.0.6
psycopg2-binary>=2.9.9
SQLAlchemy>=2.0.20
aiohttp>=3.8.6
cryptography>=41.0.3

# Dependencies with specific versions to avoid build issues
pydantic>=1.10.12,<2.0.0
pydantic_core>=2.1.2,<3.0.0

# HTTP/Websockets
urllib3>=2.0.4
websockets>=11.0.3
yarl>=1.9.2

# WSGI/ASGI Server (using specific compatible versions)
gunicorn==21.2.0
uvicorn[standard]==0.23.2  # Includes uvloop and watchfiles

# Web framework utilities
Werkzeug==2.3.7

# Database
psycopg2-binary==2.9.9
SQLAlchemy==2.0.34
alembic==1.14.1

# Redis for caching and session storage
redis>=4.5.0
