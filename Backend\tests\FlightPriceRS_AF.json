{"Success": {}, "ShoppingResponseID": {"ResponseID": {"value": "TRNa0T0BOxJbtWw9OvQMrEh7S9s7SmtDfkYOaVd70jI-AF"}}, "PricedFlightOffers": {"PricedFlightOffer": [{"OfferID": {"ObjectKey": "aea09319-7611-4ff9-a093-197611bf0001", "value": "aea09319-7611-4ff9-a093-197611bf0001", "Owner": "AF", "Channel": "NDC"}, "OfferPrice": [{"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX1", "PAX2"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "H"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA2_CHECKED_ADT"], "CarryOnReferences": ["BA1_CARRY_ON_ADT"]}, "ref": "SEG1"}, {"ClassOfService": {"Code": {"value": "K"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA2_CHECKED_ADT"], "CarryOnReferences": ["BA1_CARRY_ON_ADT"]}, "ref": "SEG2"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ1"]}}, "PriceClass": {"PriceClassReference": "PC1"}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 75308.0, "Code": "INR"}}, "BaseAmount": {"value": 54760.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 2738, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 54760.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 23286.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 13742.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 3426.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "HGL6BBLA"}, "RBD": "H"}, "FareRules": {"Penalty": {"refs": ["PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_Cancellation_ADE_225517.00_NAV_N", "PEN_Cancellation_PDE_225517.00_NAV_N", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false", "PEN_Change_PDE_200_Y", "PEN_Change_ADE_200_Y"]}}, "refs": ["SEG1", "SEG2"]}]}, "OfferItemID": "dd8ae6a9-e38d-4377-8ae6-a9e38d637782"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX3"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "H"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA2_CHECKED_CHD"], "CarryOnReferences": ["BA1_CARRY_ON_CHD"]}, "ref": "SEG1"}, {"ClassOfService": {"Code": {"value": "K"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA2_CHECKED_CHD"], "CarryOnReferences": ["BA1_CARRY_ON_CHD"]}, "ref": "SEG2"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ1"]}}, "PriceClass": {"PriceClassReference": "PC1"}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 61617.0, "Code": "INR"}}, "BaseAmount": {"value": 41070.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 2054, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 41070.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 22601.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 13742.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 2741.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "HGL6BBLA"}, "RBD": "H"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_PDE_150_Y", "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_Cancellation_ADE_225517.00_NAV_N", "PEN_Cancellation_PDE_225517.00_NAV_N", "PEN_Change_ADE_150_Y", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false"]}}, "refs": ["SEG1", "SEG2"]}]}, "OfferItemID": "ba3ae01b-347f-4a45-bae0-1b347f7a4585"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX4"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "H"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA2_CHECKED_INF"], "CarryOnReferences": ["BA1_CARRY_ON_INF"]}, "ref": "SEG1"}, {"ClassOfService": {"Code": {"value": "K"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA2_CHECKED_INF"], "CarryOnReferences": ["BA1_CARRY_ON_INF"]}, "ref": "SEG2"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ1"]}}, "PriceClass": {"PriceClassReference": "PC1"}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 5480.0, "Code": "INR"}}, "BaseAmount": {"value": 5480.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 274, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 5480.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 274.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 274.0, "Code": "INR"}, "TaxCode": "K3"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "HGL6BBLA"}, "RBD": "H"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_PDE_20_Y", "PEN_Change_ADE_20_Y", "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_Cancellation_ADE_225517.00_NAV_N", "PEN_Cancellation_PDE_225517.00_NAV_N", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false"]}}, "refs": ["SEG1", "SEG2"]}]}, "OfferItemID": "d9d27cc5-ee4c-428e-927c-c5ee4c628ec6"}], "TimeLimits": {"OfferExpiration": {"DateTime": "2025-06-03T05:01:44.764"}, "Payment": {"DateTime": "2025-06-03T23:59:00.000"}}}]}, "AirlineOffers": {"AirlineOffer": {"PricedOffer": [{"OfferID": {"ObjectKey": "d4ffe27a-9db3-46a3-bfe2-7a9db3060001", "value": "d4ffe27a-9db3-46a3-bfe2-7a9db3060001", "Owner": "AF", "Channel": "NDC"}, "OfferPrice": [{"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX1", "PAX2"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "H"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA3_CHECKED_ADT"], "CarryOnReferences": ["BA1_CARRY_ON_ADT"]}, "ref": "SEG1"}, {"ClassOfService": {"Code": {"value": "K"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA3_CHECKED_ADT"], "CarryOnReferences": ["BA1_CARRY_ON_ADT"]}, "ref": "SEG2"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ1"]}}, "PriceClass": {"PriceClassReference": "PC2"}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 80608.0, "Code": "INR"}}, "BaseAmount": {"value": 60060.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 3003, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 60060.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 23551.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 13742.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 3691.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "HGL6BBSA"}, "RBD": "H"}, "FareRules": {"Penalty": {"refs": ["PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_Cancellation_ADE_225517.00_NAV_N", "PEN_Cancellation_PDE_225517.00_NAV_N", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false", "PEN_Change_PDE_200_Y", "PEN_Change_ADE_200_Y"]}}, "refs": ["SEG1", "SEG2"]}]}, "OfferItemID": "eddffda3-09eb-40f6-9ffd-a309ebd0f66d"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX3"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "H"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA3_CHECKED_CHD"], "CarryOnReferences": ["BA1_CARRY_ON_CHD"]}, "ref": "SEG1"}, {"ClassOfService": {"Code": {"value": "K"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA3_CHECKED_CHD"], "CarryOnReferences": ["BA1_CARRY_ON_CHD"]}, "ref": "SEG2"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ1"]}}, "PriceClass": {"PriceClassReference": "PC2"}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 65593.0, "Code": "INR"}}, "BaseAmount": {"value": 45045.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 2252, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 45045.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 22800.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 13742.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 2940.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "HGL6BBSA"}, "RBD": "H"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_PDE_150_Y", "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_Cancellation_ADE_225517.00_NAV_N", "PEN_Cancellation_PDE_225517.00_NAV_N", "PEN_Change_ADE_150_Y", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false"]}}, "refs": ["SEG1", "SEG2"]}]}, "OfferItemID": "df2196b5-5758-41b0-a196-b5575831b081"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX4"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "H"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA3_CHECKED_INF_INF"], "CarryOnReferences": ["BA1_CARRY_ON_INF"]}, "ref": "SEG1"}, {"ClassOfService": {"Code": {"value": "K"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA3_CHECKED_INF_INF"], "CarryOnReferences": ["BA1_CARRY_ON_INF"]}, "ref": "SEG2"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ1"]}}, "PriceClass": {"PriceClassReference": "PC2"}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 6010.0, "Code": "INR"}}, "BaseAmount": {"value": 6010.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 301, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 6010.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 301.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 301.0, "Code": "INR"}, "TaxCode": "K3"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "HGL6BBSA"}, "RBD": "H"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_PDE_20_Y", "PEN_Change_ADE_20_Y", "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_Cancellation_ADE_225517.00_NAV_N", "PEN_Cancellation_PDE_225517.00_NAV_N", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false"]}}, "refs": ["SEG1", "SEG2"]}]}, "OfferItemID": "ca2362db-dea9-4730-a362-dbdea907303f"}], "TimeLimits": {"OfferExpiration": {"DateTime": "2025-06-03T05:03:15.451"}, "Payment": {"DateTime": "2025-06-03T23:59:00.000"}}}, {"OfferID": {"ObjectKey": "d4ffe27a-9db3-46a3-bfe2-7a9db3060002", "value": "d4ffe27a-9db3-46a3-bfe2-7a9db3060002", "Owner": "AF", "Channel": "NDC"}, "OfferPrice": [{"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX1", "PAX2"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "H"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA3_CHECKED_ADT"], "CarryOnReferences": ["BA1_CARRY_ON_ADT"]}, "ref": "SEG1"}, {"ClassOfService": {"Code": {"value": "K"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA3_CHECKED_ADT"], "CarryOnReferences": ["BA1_CARRY_ON_ADT"]}, "ref": "SEG2"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ1"]}}, "PriceClass": {"PriceClassReference": "PC3"}, "AssociatedService": {"ServiceReferences": ["SRV-SEAT"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 88357.0, "Code": "INR"}}, "BaseAmount": {"value": 67810.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 3391, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 67810.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 23938.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 13742.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 4078.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "HGL6BFFA"}, "RBD": "H"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_PDE_0_N", "PEN_Cancellation_PDE_0_N", "PEN_Cancellation_ADE_0_N", "PEN_Change_ADE_0_N", "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false"]}}, "refs": ["SEG1", "SEG2"]}]}, "OfferItemID": "b07af113-0edc-4600-baf1-130edcc600ba"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX3"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "H"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA3_CHECKED_CHD"], "CarryOnReferences": ["BA1_CARRY_ON_CHD"]}, "ref": "SEG1"}, {"ClassOfService": {"Code": {"value": "K"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA3_CHECKED_CHD"], "CarryOnReferences": ["BA1_CARRY_ON_CHD"]}, "ref": "SEG2"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ1"]}}, "PriceClass": {"PriceClassReference": "PC3"}, "AssociatedService": {"ServiceReferences": ["SRV-SEAT"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 71408.0, "Code": "INR"}}, "BaseAmount": {"value": 50860.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 2543, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 50860.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 23091.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 13742.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 3231.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "HGL6BFFA"}, "RBD": "H"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_PDE_0_N", "PEN_Cancellation_PDE_0_N", "PEN_Cancellation_ADE_0_N", "PEN_Change_ADE_0_N", "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false"]}}, "refs": ["SEG1", "SEG2"]}]}, "OfferItemID": "d2756a36-7d82-4a80-b56a-367d822a80f4"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX4"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "H"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA3_CHECKED_INF_INF"], "CarryOnReferences": ["BA1_CARRY_ON_INF"]}, "ref": "SEG1"}, {"ClassOfService": {"Code": {"value": "K"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG1"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA3_CHECKED_INF_INF"], "CarryOnReferences": ["BA1_CARRY_ON_INF"]}, "ref": "SEG2"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ1"]}}, "PriceClass": {"PriceClassReference": "PC3"}, "AssociatedService": {"ServiceReferences": ["SRV-SEAT"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 6786.0, "Code": "INR"}}, "BaseAmount": {"value": 6785.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 339, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 6785.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 340.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 340.0, "Code": "INR"}, "TaxCode": "K3"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "HGL6BFFA"}, "RBD": "H"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_PDE_0_N", "PEN_Cancellation_PDE_0_N", "PEN_Cancellation_ADE_0_N", "PEN_Change_ADE_0_N", "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false"]}}, "refs": ["SEG1", "SEG2"]}]}, "OfferItemID": "d8525bdc-b3a4-4d47-925b-dcb3a45d47b1"}], "TimeLimits": {"OfferExpiration": {"DateTime": "2025-06-03T05:03:15.451"}, "Payment": {"DateTime": "2025-06-03T23:59:00.000"}}}, {"OfferID": {"ObjectKey": "d4ffe27a-9db3-46a3-bfe2-7a9db3060003", "value": "d4ffe27a-9db3-46a3-bfe2-7a9db3060003", "Owner": "AF", "Channel": "NDC"}, "OfferPrice": [{"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX1", "PAX2"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "S"}, "MarketingName": {"value": "PREMIUM", "CabinDesignator": "W"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA2_CHECKED_ADT"], "CarryOnReferences": ["BA4_CARRY_ON_ADT"]}, "ref": "SEG105"}, {"ClassOfService": {"Code": {"value": "Y"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA2_CHECKED_ADT"], "CarryOnReferences": ["BA4_CARRY_ON_ADT"]}, "ref": "SEG106"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ2"]}}, "PriceClass": {"PriceClassReference": "PC4"}, "AssociatedService": {"ServiceReferences": ["SRVID1_SKY"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 121228.0, "Code": "INR"}}, "BaseAmount": {"value": 93195.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 4660, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 93195.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 32693.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 13742.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 12833.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "SFL6BBLA"}, "RBD": "S"}, "FareRules": {"Penalty": {"refs": ["PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_Cancellation_ADE_225517.00_NAV_N", "PEN_Cancellation_PDE_225517.00_NAV_N", "PEN_Change_PDE_350_Y", "PEN_Change_ADE_350_Y", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false"]}}, "refs": ["SEG105", "SEG106"]}]}, "OfferItemID": "d8d0cf53-4b3b-4b80-90cf-534b3b7b8074"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX3"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "S"}, "MarketingName": {"value": "PREMIUM", "CabinDesignator": "W"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA2_CHECKED_CHD"], "CarryOnReferences": ["BA4_CARRY_ON_CHD"]}, "ref": "SEG105"}, {"ClassOfService": {"Code": {"value": "Y"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA2_CHECKED_CHD"], "CarryOnReferences": ["BA4_CARRY_ON_CHD"]}, "ref": "SEG106"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ2"]}}, "PriceClass": {"PriceClassReference": "PC4"}, "AssociatedService": {"ServiceReferences": ["SRVID1_SKY"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 96302.0, "Code": "INR"}}, "BaseAmount": {"value": 69900.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 3495, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 69900.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 29897.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 13742.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 10037.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "SFL6BBLA"}, "RBD": "S"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_ADE_263_Y", "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_Cancellation_ADE_225517.00_NAV_N", "PEN_Cancellation_PDE_225517.00_NAV_N", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false", "PEN_Change_PDE_263_Y"]}}, "refs": ["SEG105", "SEG106"]}]}, "OfferItemID": "b0877a71-af10-4f9c-877a-71af109f9c55"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX4"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "S"}, "MarketingName": {"value": "PREMIUM", "CabinDesignator": "W"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA2_CHECKED_INF"]}, "ref": "SEG105"}, {"ClassOfService": {"Code": {"value": "Y"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA2_CHECKED_INF"]}, "ref": "SEG106"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ2"]}}, "PriceClass": {"PriceClassReference": "PC4"}, "AssociatedService": {"ServiceReferences": ["SRVID1_SKY"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 9973.0, "Code": "INR"}}, "BaseAmount": {"value": 9320.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 466, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 9320.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 1119.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 1119.0, "Code": "INR"}, "TaxCode": "K3"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "SFL6BBLA"}, "RBD": "S"}, "FareRules": {"Penalty": {"refs": ["PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_Cancellation_ADE_225517.00_NAV_N", "PEN_Cancellation_PDE_225517.00_NAV_N", "PEN_Change_PDE_35_Y", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false", "PEN_Change_ADE_35_Y"]}}, "refs": ["SEG105", "SEG106"]}]}, "OfferItemID": "a24ca1c0-d555-4926-8ca1-c0d5558926d1"}], "TimeLimits": {"OfferExpiration": {"DateTime": "2025-06-03T05:03:15.451"}, "Payment": {"DateTime": "2025-06-03T23:59:00.000"}}}, {"OfferID": {"ObjectKey": "d4ffe27a-9db3-46a3-bfe2-7a9db3060004", "value": "d4ffe27a-9db3-46a3-bfe2-7a9db3060004", "Owner": "AF", "Channel": "NDC"}, "OfferPrice": [{"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX1", "PAX2"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "S"}, "MarketingName": {"value": "PREMIUM", "CabinDesignator": "W"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_ADT"], "CarryOnReferences": ["BA4_CARRY_ON_ADT"]}, "ref": "SEG105"}, {"ClassOfService": {"Code": {"value": "Y"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_ADT"], "CarryOnReferences": ["BA4_CARRY_ON_ADT"]}, "ref": "SEG106"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ2"]}}, "PriceClass": {"PriceClassReference": "PC5"}, "AssociatedService": {"ServiceReferences": ["SRVID1_SKY"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 128023.0, "Code": "INR"}}, "BaseAmount": {"value": 99545.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 4977, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 99545.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 33455.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 13742.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 13595.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "SFL6BBJA"}, "RBD": "S"}, "FareRules": {"Penalty": {"refs": ["PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_Cancellation_ADE_225517.00_NAV_N", "PEN_Cancellation_PDE_225517.00_NAV_N", "PEN_Change_ADE_250_Y", "PEN_Change_PDE_250_Y", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false"]}}, "refs": ["SEG105", "SEG106"]}]}, "OfferItemID": "bd155137-ac3d-4c37-9551-37ac3d0c371e"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX3"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "S"}, "MarketingName": {"value": "PREMIUM", "CabinDesignator": "W"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_CHD"], "CarryOnReferences": ["BA4_CARRY_ON_CHD"]}, "ref": "SEG105"}, {"ClassOfService": {"Code": {"value": "Y"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_CHD"], "CarryOnReferences": ["BA4_CARRY_ON_CHD"]}, "ref": "SEG106"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ2"]}}, "PriceClass": {"PriceClassReference": "PC5"}, "AssociatedService": {"ServiceReferences": ["SRVID1_SKY"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 101396.0, "Code": "INR"}}, "BaseAmount": {"value": 74660.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 3733, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 74660.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 30469.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 13742.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 10609.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "SFL6BBJA"}, "RBD": "S"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_PDE_188_Y", "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_Cancellation_ADE_225517.00_NAV_N", "PEN_Cancellation_PDE_225517.00_NAV_N", "PEN_Change_ADE_188_Y", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false"]}}, "refs": ["SEG105", "SEG106"]}]}, "OfferItemID": "e4193fb3-ca85-4a08-993f-b3ca850a08ed"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX4"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "S"}, "MarketingName": {"value": "PREMIUM", "CabinDesignator": "W"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_INF_INF"]}, "ref": "SEG105"}, {"ClassOfService": {"Code": {"value": "Y"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_INF_INF"]}, "ref": "SEG106"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ2"]}}, "PriceClass": {"PriceClassReference": "PC5"}, "AssociatedService": {"ServiceReferences": ["SRVID1_SKY"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 10652.0, "Code": "INR"}}, "BaseAmount": {"value": 9955.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 498, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 9955.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 1195.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 1195.0, "Code": "INR"}, "TaxCode": "K3"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "SFL6BBJA"}, "RBD": "S"}, "FareRules": {"Penalty": {"refs": ["PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_Cancellation_ADE_225517.00_NAV_N", "PEN_Change_PDE_25_Y", "PEN_Cancellation_PDE_225517.00_NAV_N", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false", "PEN_Change_ADE_25_Y"]}}, "refs": ["SEG105", "SEG106"]}]}, "OfferItemID": "cc56ace9-1ee7-480f-96ac-e91ee7980f64"}], "TimeLimits": {"OfferExpiration": {"DateTime": "2025-06-03T05:03:15.451"}, "Payment": {"DateTime": "2025-06-03T23:59:00.000"}}}, {"OfferID": {"ObjectKey": "d4ffe27a-9db3-46a3-bfe2-7a9db3060005", "value": "d4ffe27a-9db3-46a3-bfe2-7a9db3060005", "Owner": "AF", "Channel": "NDC"}, "OfferPrice": [{"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX1", "PAX2"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "S"}, "MarketingName": {"value": "PREMIUM", "CabinDesignator": "W"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_ADT"], "CarryOnReferences": ["BA4_CARRY_ON_ADT"]}, "ref": "SEG105"}, {"ClassOfService": {"Code": {"value": "Y"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_ADT"], "CarryOnReferences": ["BA4_CARRY_ON_ADT"]}, "ref": "SEG106"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ2"]}}, "PriceClass": {"PriceClassReference": "PC6"}, "AssociatedService": {"ServiceReferences": ["SRV-SEAT", "SRVID1_SKY"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 142468.0, "Code": "INR"}}, "BaseAmount": {"value": 113045.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 5652, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 113045.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 35075.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 13742.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 15215.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "SFL6BFKA"}, "RBD": "S"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_PDE_0_N", "PEN_Cancellation_PDE_0_N", "PEN_Cancellation_ADE_0_N", "PEN_Change_ADE_0_N", "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false"]}}, "refs": ["SEG105", "SEG106"]}]}, "OfferItemID": "dd1f750a-4dcd-4b4b-9f75-0a4dcd0b4bf9"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX3"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "S"}, "MarketingName": {"value": "PREMIUM", "CabinDesignator": "W"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_CHD"], "CarryOnReferences": ["BA4_CARRY_ON_CHD"]}, "ref": "SEG105"}, {"ClassOfService": {"Code": {"value": "Y"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_CHD"], "CarryOnReferences": ["BA4_CARRY_ON_CHD"]}, "ref": "SEG106"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ2"]}}, "PriceClass": {"PriceClassReference": "PC6"}, "AssociatedService": {"ServiceReferences": ["SRV-SEAT", "SRVID1_SKY"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 112230.0, "Code": "INR"}}, "BaseAmount": {"value": 84785.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 4239, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 84785.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 31684.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 13742.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 11824.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "SFL6BFKA"}, "RBD": "S"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_PDE_0_N", "PEN_Cancellation_PDE_0_N", "PEN_Cancellation_ADE_0_N", "PEN_Change_ADE_0_N", "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false"]}}, "refs": ["SEG105", "SEG106"]}]}, "OfferItemID": "b4c08bab-409b-40c4-808b-ab409b50c4b7"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX4"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "S"}, "MarketingName": {"value": "PREMIUM", "CabinDesignator": "W"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_INF_INF"]}, "ref": "SEG105"}, {"ClassOfService": {"Code": {"value": "Y"}, "MarketingName": {"value": "ECONOMY", "CabinDesignator": "Y"}, "refs": ["FG-SEG105"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_INF_INF"]}, "ref": "SEG106"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ2"]}}, "PriceClass": {"PriceClassReference": "PC6"}, "AssociatedService": {"ServiceReferences": ["SRV-SEAT", "SRVID1_SKY"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 12097.0, "Code": "INR"}}, "BaseAmount": {"value": 11305.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 565, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 11305.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 1357.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 1357.0, "Code": "INR"}, "TaxCode": "K3"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "SFL6BFKA"}, "RBD": "S"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_PDE_0_N", "PEN_Cancellation_PDE_0_N", "PEN_Cancellation_ADE_0_N", "PEN_Change_ADE_0_N", "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false"]}}, "refs": ["SEG105", "SEG106"]}]}, "OfferItemID": "e5e5e530-5481-4548-a5e5-305481c54835"}], "TimeLimits": {"OfferExpiration": {"DateTime": "2025-06-03T05:03:15.451"}, "Payment": {"DateTime": "2025-06-03T23:59:00.000"}}}, {"OfferID": {"ObjectKey": "d4ffe27a-9db3-46a3-bfe2-7a9db3060006", "value": "d4ffe27a-9db3-46a3-bfe2-7a9db3060006", "Owner": "AF", "Channel": "NDC"}, "OfferPrice": [{"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX1", "PAX2"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "I"}, "MarketingName": {"value": "BUSINESS", "CabinDesignator": "C"}, "refs": ["FG-SEG111"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_ADT"], "CarryOnReferences": ["BA4_CARRY_ON_ADT"]}, "ref": "SEG111"}, {"ClassOfService": {"Code": {"value": "J"}, "MarketingName": {"value": "BUSINESS", "CabinDesignator": "C"}, "refs": ["FG-SEG111"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_ADT"], "CarryOnReferences": ["BA4_CARRY_ON_ADT"]}, "ref": "SEG112"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ3"]}}, "PriceClass": {"PriceClassReference": "PC7"}, "AssociatedService": {"ServiceReferences": ["SRVID1_SKY", "SRVID2_LOUNGE_ACCESS"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 215584.0, "Code": "INR"}}, "BaseAmount": {"value": 157105.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 7855, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 157105.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 66334.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 36931.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 23285.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "IGS0BBJA"}, "RBD": "I"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_ADE_300_Y", "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_Cancellation_ADE_225517.00_NAV_N", "PEN_Cancellation_PDE_225517.00_NAV_N", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false", "PEN_Change_PDE_300_Y"]}}, "refs": ["SEG111", "SEG112"]}]}, "OfferItemID": "b5b1a33d-4cef-41a0-b1a3-3d4cefc1a024"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX3"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "I"}, "MarketingName": {"value": "BUSINESS", "CabinDesignator": "C"}, "refs": ["FG-SEG111"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_CHD"], "CarryOnReferences": ["BA4_CARRY_ON_CHD"]}, "ref": "SEG111"}, {"ClassOfService": {"Code": {"value": "J"}, "MarketingName": {"value": "BUSINESS", "CabinDesignator": "C"}, "refs": ["FG-SEG111"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_CHD"], "CarryOnReferences": ["BA4_CARRY_ON_CHD"]}, "ref": "SEG112"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ3"]}}, "PriceClass": {"PriceClassReference": "PC7"}, "AssociatedService": {"ServiceReferences": ["SRVID1_SKY", "SRVID2_LOUNGE_ACCESS"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 173559.0, "Code": "INR"}}, "BaseAmount": {"value": 117830.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 5892, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 117830.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 61621.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 36931.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 18572.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "IGS0BBJA"}, "RBD": "I"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_ADE_225_Y", "PEN_Change_PDE_225_Y", "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_Cancellation_ADE_225517.00_NAV_N", "PEN_Cancellation_PDE_225517.00_NAV_N", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false"]}}, "refs": ["SEG111", "SEG112"]}]}, "OfferItemID": "dfbfea09-e295-439e-bfea-09e295a39e14"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX4"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "I"}, "MarketingName": {"value": "BUSINESS", "CabinDesignator": "C"}, "refs": ["FG-SEG111"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_INF_INF"]}, "ref": "SEG111"}, {"ClassOfService": {"Code": {"value": "J"}, "MarketingName": {"value": "BUSINESS", "CabinDesignator": "C"}, "refs": ["FG-SEG111"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_INF_INF"]}, "ref": "SEG112"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ3"]}}, "PriceClass": {"PriceClassReference": "PC7"}, "AssociatedService": {"ServiceReferences": ["SRVID1_SKY", "SRVID2_LOUNGE_ACCESS"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 16810.0, "Code": "INR"}}, "BaseAmount": {"value": 15710.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 786, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 15710.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 1886.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 1886.0, "Code": "INR"}, "TaxCode": "K3"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "IGS0BBJA"}, "RBD": "I"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_PDE_30_Y", "PEN_Change_ADE_30_Y", "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "PEN_Cancellation_ADE_225517.00_NAV_N", "PEN_Cancellation_PDE_225517.00_NAV_N", "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false"]}}, "refs": ["SEG111", "SEG112"]}]}, "OfferItemID": "ca192a97-f022-484b-992a-97f022884b44"}], "TimeLimits": {"OfferExpiration": {"DateTime": "2025-06-03T05:03:15.451"}, "Payment": {"DateTime": "2025-06-06T05:00:00.000"}}}, {"OfferID": {"ObjectKey": "d4ffe27a-9db3-46a3-bfe2-7a9db3060007", "value": "d4ffe27a-9db3-46a3-bfe2-7a9db3060007", "Owner": "AF", "Channel": "NDC"}, "OfferPrice": [{"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX1", "PAX2"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "I"}, "MarketingName": {"value": "BUSINESS", "CabinDesignator": "C"}, "refs": ["FG-SEG111"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_ADT"], "CarryOnReferences": ["BA4_CARRY_ON_ADT"]}, "ref": "SEG111"}, {"ClassOfService": {"Code": {"value": "J"}, "MarketingName": {"value": "BUSINESS", "CabinDesignator": "C"}, "refs": ["FG-SEG111"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_ADT"], "CarryOnReferences": ["BA4_CARRY_ON_ADT"]}, "ref": "SEG112"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ3"]}}, "PriceClass": {"PriceClassReference": "PC8"}, "AssociatedService": {"ServiceReferences": ["SRV-SEAT", "SRVID1_SKY", "SRVID2_LOUNGE_ACCESS"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 235379.0, "Code": "INR"}}, "BaseAmount": {"value": 175605.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 8780, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 175605.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 68554.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 36931.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 25505.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "IGS0BFKA"}, "RBD": "I"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_PDE_0_N", "PEN_NoShow_NOSHOW_CANCELLATION_0_false", "PEN_Cancellation_PDE_0_N", "PEN_Cancellation_ADE_0_N", "PEN_Change_ADE_0_N", "PEN_NoShow_NOSHOW_CHANGE_0_false"]}}, "refs": ["SEG111", "SEG112"]}]}, "OfferItemID": "e70b9d9e-645c-4c27-8b9d-9e645c0c27b5"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX3"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "I"}, "MarketingName": {"value": "BUSINESS", "CabinDesignator": "C"}, "refs": ["FG-SEG111"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_CHD"], "CarryOnReferences": ["BA4_CARRY_ON_CHD"]}, "ref": "SEG111"}, {"ClassOfService": {"Code": {"value": "J"}, "MarketingName": {"value": "BUSINESS", "CabinDesignator": "C"}, "refs": ["FG-SEG111"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_CHD"], "CarryOnReferences": ["BA4_CARRY_ON_CHD"]}, "ref": "SEG112"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ3"]}}, "PriceClass": {"PriceClassReference": "PC8"}, "AssociatedService": {"ServiceReferences": ["SRV-SEAT", "SRVID1_SKY", "SRVID2_LOUNGE_ACCESS"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 188406.0, "Code": "INR"}}, "BaseAmount": {"value": 131705.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 6585, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 131705.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 63286.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 36931.0, "Code": "INR"}, "TaxCode": "YR"}, {"Amount": {"value": 1770.0, "Code": "INR"}, "TaxCode": "IN"}, {"Amount": {"value": 20237.0, "Code": "INR"}, "TaxCode": "K3"}, {"Amount": {"value": 1217.0, "Code": "INR"}, "TaxCode": "P2"}, {"Amount": {"value": 1209.0, "Code": "INR"}, "TaxCode": "CJ"}, {"Amount": {"value": 1436.0, "Code": "INR"}, "TaxCode": "RN"}, {"Amount": {"value": 486.0, "Code": "INR"}, "TaxCode": "T02"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "IGS0BFKA"}, "RBD": "I"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_PDE_0_N", "PEN_NoShow_NOSHOW_CANCELLATION_0_false", "PEN_Cancellation_PDE_0_N", "PEN_Cancellation_ADE_0_N", "PEN_Change_ADE_0_N", "PEN_NoShow_NOSHOW_CHANGE_0_false"]}}, "refs": ["SEG111", "SEG112"]}]}, "OfferItemID": "df41f0b1-c6b4-4d44-81f0-b1c6b4ad44d9"}, {"RequestedDate": {"Associations": [{"AssociatedTraveler": {"TravelerReferences": ["PAX4"]}, "ApplicableFlight": {"FlightSegmentReference": [{"ClassOfService": {"Code": {"value": "I"}, "MarketingName": {"value": "BUSINESS", "CabinDesignator": "C"}, "refs": ["FG-SEG111"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_INF_INF"]}, "ref": "SEG111"}, {"ClassOfService": {"Code": {"value": "J"}, "MarketingName": {"value": "BUSINESS", "CabinDesignator": "C"}, "refs": ["FG-SEG111"]}, "BagDetailAssociation": {"CheckedBagReferences": ["BA6_CHECKED_INF_INF"]}, "ref": "SEG112"}], "OriginDestinationReferences": ["OD1"], "FlightReferences": {"value": ["PJ3"]}}, "PriceClass": {"PriceClassReference": "PC8"}, "AssociatedService": {"ServiceReferences": ["SRV-SEAT", "SRVID1_SKY", "SRVID2_LOUNGE_ACCESS"]}}], "PriceDetail": {"TotalAmount": {"SimpleCurrencyPrice": {"value": 18790.0, "Code": "INR"}}, "BaseAmount": {"value": 17560.0, "Code": "INR"}, "Discount": [{"DiscountAmount": {"value": 878, "Code": "INR"}, "DiscountPercent": 5, "discountOwner": "Magellan Travel Services (P) Ltd.", "discountCode": "Disc_rea", "discountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preDiscountedAmount": {"value": 17560.0, "Code": "INR"}}], "Taxes": {"Total": {"value": 2108.0, "Code": "INR"}, "Breakdown": {"Tax": [{"Amount": {"value": 2108.0, "Code": "INR"}, "TaxCode": "K3"}]}}}}, "FareDetail": {"FareComponent": [{"FareBasis": {"FareBasisCode": {"Code": "IGS0BFKA"}, "RBD": "I"}, "FareRules": {"Penalty": {"refs": ["PEN_Change_PDE_0_N", "PEN_NoShow_NOSHOW_CANCELLATION_0_false", "PEN_Cancellation_PDE_0_N", "PEN_Cancellation_ADE_0_N", "PEN_Change_ADE_0_N", "PEN_NoShow_NOSHOW_CHANGE_0_false"]}}, "refs": ["SEG111", "SEG112"]}]}, "OfferItemID": "ecea032f-0ef5-4ccd-aa03-2f0ef56ccda9"}], "TimeLimits": {"OfferExpiration": {"DateTime": "2025-06-03T05:03:15.451"}, "Payment": {"DateTime": "2025-06-06T05:00:00.000"}}}]}}, "DataLists": {"AnonymousTravelerList": {"AnonymousTraveler": [{"ObjectKey": "PAX1", "PTC": {"value": "ADT"}}, {"ObjectKey": "PAX2", "PTC": {"value": "ADT"}}, {"ObjectKey": "PAX3", "PTC": {"value": "CHD"}}, {"ObjectKey": "PAX4", "PTC": {"value": "INF"}}]}, "CarryOnAllowanceList": {"CarryOnAllowance": [{"ListKey": "BA1_CARRY_ON_INF", "AllowanceDescription": {"ApplicableParty": "Traveler", "Descriptions": {"Description": [{"Text": {"value": "Height 13.8 IN"}}, {"Text": {"value": "Width 9.9 IN"}}, {"Text": {"value": "Length 21.7 IN"}}, {"Text": {"value": "Total Linear 45.4 IN"}}]}}, "PieceAllowance": [{"ApplicableParty": "Traveler", "TotalQuantity": 1}], "WeightAllowance": {"ApplicableParty": "Traveler", "MaximumWeight": [{"Value": 12.0, "UOM": "Kilogram"}, {"Value": 26.0, "UOM": "Pound"}]}}, {"ListKey": "BA4_CARRY_ON_INF", "AllowanceDescription": {"ApplicableParty": "Traveler", "Descriptions": {"Description": [{"Text": {"value": "Height 13.8 IN"}}, {"Text": {"value": "Width 9.9 IN"}}, {"Text": {"value": "Length 21.7 IN"}}, {"Text": {"value": "Total Linear 45.4 IN"}}]}}, "PieceAllowance": [{"ApplicableParty": "Traveler", "TotalQuantity": 2}]}, {"ListKey": "BA5_CARRY_ON_INF", "AllowanceDescription": {"ApplicableParty": "Traveler", "Descriptions": {"Description": [{"Text": {"value": "Height 13.8 IN"}}, {"Text": {"value": "Width 9.9 IN"}}, {"Text": {"value": "Length 21.7 IN"}}, {"Text": {"value": "Total Linear 45.4 IN"}}]}}, "PieceAllowance": [{"ApplicableParty": "Traveler", "TotalQuantity": 1}]}, {"ListKey": "BA1_CARRY_ON_ADT", "AllowanceDescription": {"ApplicableParty": "Traveler", "Descriptions": {"Description": [{"Text": {"value": "Height 13.8 IN"}}, {"Text": {"value": "Width 9.9 IN"}}, {"Text": {"value": "Length 21.7 IN"}}, {"Text": {"value": "Total Linear 45.4 IN"}}]}}, "PieceAllowance": [{"ApplicableParty": "Traveler", "TotalQuantity": 1}], "WeightAllowance": {"ApplicableParty": "Traveler", "MaximumWeight": [{"Value": 12.0, "UOM": "Kilogram"}, {"Value": 26.0, "UOM": "Pound"}]}}, {"ListKey": "BA4_CARRY_ON_ADT", "AllowanceDescription": {"ApplicableParty": "Traveler", "Descriptions": {"Description": [{"Text": {"value": "Height 13.8 IN"}}, {"Text": {"value": "Width 9.9 IN"}}, {"Text": {"value": "Length 21.7 IN"}}, {"Text": {"value": "Total Linear 45.4 IN"}}]}}, "PieceAllowance": [{"ApplicableParty": "Traveler", "TotalQuantity": 2}]}, {"ListKey": "BA5_CARRY_ON_ADT", "AllowanceDescription": {"ApplicableParty": "Traveler", "Descriptions": {"Description": [{"Text": {"value": "Height 13.8 IN"}}, {"Text": {"value": "Width 9.9 IN"}}, {"Text": {"value": "Length 21.7 IN"}}, {"Text": {"value": "Total Linear 45.4 IN"}}]}}, "PieceAllowance": [{"ApplicableParty": "Traveler", "TotalQuantity": 1}]}, {"ListKey": "BA1_CARRY_ON_CHD", "AllowanceDescription": {"ApplicableParty": "Traveler", "Descriptions": {"Description": [{"Text": {"value": "Height 13.8 IN"}}, {"Text": {"value": "Width 9.9 IN"}}, {"Text": {"value": "Length 21.7 IN"}}, {"Text": {"value": "Total Linear 45.4 IN"}}]}}, "PieceAllowance": [{"ApplicableParty": "Traveler", "TotalQuantity": 1}], "WeightAllowance": {"ApplicableParty": "Traveler", "MaximumWeight": [{"Value": 12.0, "UOM": "Kilogram"}, {"Value": 26.0, "UOM": "Pound"}]}}, {"ListKey": "BA4_CARRY_ON_CHD", "AllowanceDescription": {"ApplicableParty": "Traveler", "Descriptions": {"Description": [{"Text": {"value": "Height 13.8 IN"}}, {"Text": {"value": "Width 9.9 IN"}}, {"Text": {"value": "Length 21.7 IN"}}, {"Text": {"value": "Total Linear 45.4 IN"}}]}}, "PieceAllowance": [{"ApplicableParty": "Traveler", "TotalQuantity": 2}]}, {"ListKey": "BA5_CARRY_ON_CHD", "AllowanceDescription": {"ApplicableParty": "Traveler", "Descriptions": {"Description": [{"Text": {"value": "Height 13.8 IN"}}, {"Text": {"value": "Width 9.9 IN"}}, {"Text": {"value": "Length 21.7 IN"}}, {"Text": {"value": "Total Linear 45.4 IN"}}]}}, "PieceAllowance": [{"ApplicableParty": "Traveler", "TotalQuantity": 1}]}]}, "CheckedBagAllowanceList": {"CheckedBagAllowance": [{"ListKey": "BA2_CHECKED_INF", "AllowanceDescription": {"Descriptions": {"Description": [{"Text": {"value": "Baggage Not Applicable"}}]}}, "PieceAllowance": [{"ApplicableParty": "Traveler", "TotalQuantity": 0}]}, {"ListKey": "BA2_CHECKED_ADT", "AllowanceDescription": {"Descriptions": {"Description": [{"Text": {"value": "Baggage Not Applicable"}}]}}, "PieceAllowance": [{"ApplicableParty": "Traveler", "TotalQuantity": 0}]}, {"ListKey": "BA2_CHECKED_CHD", "AllowanceDescription": {"Descriptions": {"Description": [{"Text": {"value": "Baggage Not Applicable"}}]}}, "PieceAllowance": [{"ApplicableParty": "Traveler", "TotalQuantity": 0}]}]}, "FareList": {"FareGroup": [{"ListKey": "FG-SEG1", "Fare": {"FareCode": {"Code": "70J"}, "FareDetail": {"Remarks": {"Remark": [{"value": "PUBL"}]}}}, "FareBasisCode": {"Code": "HGL6BBLA"}}, {"ListKey": "FG-SEG105", "Fare": {"FareCode": {"Code": "70J"}, "FareDetail": {"Remarks": {"Remark": [{"value": "PUBL"}]}}}, "FareBasisCode": {"Code": "SFL6BBLA"}}, {"ListKey": "FG-SEG111", "Fare": {"FareCode": {"Code": "70J"}, "FareDetail": {"Remarks": {"Remark": [{"value": "PUBL"}]}}}, "FareBasisCode": {"Code": "IGS0BBJA"}}]}, "FlightSegmentList": {"FlightSegment": [{"SegmentKey": "SEG1", "Departure": {"AirportCode": {"value": "BLR"}, "Date": "2025-06-10T01:10:00.000", "Time": "01:10", "Terminal": {"Name": "2"}}, "Arrival": {"AirportCode": {"value": "AMS"}, "Date": "2025-06-10T07:45:00.000", "Time": "07:45"}, "MarketingCarrier": {"AirlineID": {"value": "KL"}, "FlightNumber": {"value": "880"}}, "OperatingCarrier": {"AirlineID": {"value": "KL"}}, "Equipment": {"AircraftCode": {"value": "772"}}, "ClassOfService": {"Code": {"value": "H"}, "refs": ["FG-SEG1"]}, "FlightDetail": {"FlightDuration": {"Value": "PT14H5M"}}}, {"SegmentKey": "SEG2", "Departure": {"AirportCode": {"value": "AMS"}, "Date": "2025-06-10T10:20:00.000", "Time": "10:20"}, "Arrival": {"AirportCode": {"value": "LHR"}, "Date": "2025-06-10T10:45:00.000", "Time": "10:45", "Terminal": {"Name": "4"}}, "MarketingCarrier": {"AirlineID": {"value": "KL"}, "FlightNumber": {"value": "1005"}}, "OperatingCarrier": {"AirlineID": {"value": "KL"}}, "Equipment": {"AircraftCode": {"value": "32Q"}}, "ClassOfService": {"Code": {"value": "K"}, "refs": ["FG-SEG2"]}, "FlightDetail": {"FlightDuration": {"Value": "PT14H5M"}}}, {"SegmentKey": "SEG105", "Departure": {"AirportCode": {"value": "BLR"}, "Date": "2025-06-10T01:10:00.000", "Time": "01:10", "Terminal": {"Name": "2"}}, "Arrival": {"AirportCode": {"value": "AMS"}, "Date": "2025-06-10T07:45:00.000", "Time": "07:45"}, "MarketingCarrier": {"AirlineID": {"value": "KL"}, "FlightNumber": {"value": "880"}}, "OperatingCarrier": {"AirlineID": {"value": "KL"}}, "Equipment": {"AircraftCode": {"value": "781"}}, "ClassOfService": {"Code": {"value": "S"}, "refs": ["FG-SEG105"]}, "FlightDetail": {"FlightDuration": {"Value": "PT14H5M"}}}, {"SegmentKey": "SEG106", "Departure": {"AirportCode": {"value": "AMS"}, "Date": "2025-06-10T10:20:00.000", "Time": "10:20"}, "Arrival": {"AirportCode": {"value": "LHR"}, "Date": "2025-06-10T10:45:00.000", "Time": "10:45", "Terminal": {"Name": "4"}}, "MarketingCarrier": {"AirlineID": {"value": "KL"}, "FlightNumber": {"value": "1005"}}, "OperatingCarrier": {"AirlineID": {"value": "KL"}}, "Equipment": {"AircraftCode": {"value": "32Q"}}, "ClassOfService": {"Code": {"value": "Y"}, "refs": ["FG-SEG106"]}, "FlightDetail": {"FlightDuration": {"Value": "PT14H5M"}}}, {"SegmentKey": "SEG111", "Departure": {"AirportCode": {"value": "BLR"}, "Date": "2025-06-10T01:10:00.000", "Time": "01:10", "Terminal": {"Name": "2"}}, "Arrival": {"AirportCode": {"value": "AMS"}, "Date": "2025-06-10T07:45:00.000", "Time": "07:45"}, "MarketingCarrier": {"AirlineID": {"value": "KL"}, "FlightNumber": {"value": "880"}}, "OperatingCarrier": {"AirlineID": {"value": "KL"}}, "Equipment": {"AircraftCode": {"value": "781"}}, "ClassOfService": {"Code": {"value": "I"}, "refs": ["FG-SEG111"]}, "FlightDetail": {"FlightDuration": {"Value": "PT14H5M"}}}, {"SegmentKey": "SEG112", "Departure": {"AirportCode": {"value": "AMS"}, "Date": "2025-06-10T10:20:00.000", "Time": "10:20"}, "Arrival": {"AirportCode": {"value": "LHR"}, "Date": "2025-06-10T10:45:00.000", "Time": "10:45", "Terminal": {"Name": "4"}}, "MarketingCarrier": {"AirlineID": {"value": "KL"}, "FlightNumber": {"value": "1005"}}, "OperatingCarrier": {"AirlineID": {"value": "KL"}}, "Equipment": {"AircraftCode": {"value": "32Q"}}, "ClassOfService": {"Code": {"value": "J"}, "refs": ["FG-SEG112"]}, "FlightDetail": {"FlightDuration": {"Value": "PT14H5M"}}}]}, "FlightList": {"Flight": [{"FlightKey": "PJ1", "SegmentReferences": {"value": ["SEG1", "SEG2"]}}, {"FlightKey": "PJ2", "SegmentReferences": {"value": ["SEG105", "SEG106"]}}, {"FlightKey": "PJ3", "SegmentReferences": {"value": ["SEG111", "SEG112"]}}]}, "OriginDestinationList": {"OriginDestination": [{"OriginDestinationKey": "OD1", "FlightReferences": {"value": ["PJ1", "PJ2", "PJ3"]}, "DepartureCode": {"value": "BLR"}, "ArrivalCode": {"value": "LHR"}}]}, "PenaltyList": {"Penalty": [{"ObjectKey": "PEN_NoShow_NOSHOW_CHANGE_225517.00_false", "Details": {"Detail": [{"Type": "Change-Noshow", "Application": {"Code": "1"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 225517.0, "Code": "INR"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "false"}]}}, {"CurrencyAmountValue": {"value": 225517.0, "Code": "INR"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "false"}]}}]}}]}, "ChangeFeeInd": false, "ChangeAllowedInd": false}, {"ObjectKey": "PEN_NoShow_NOSHOW_CANCELLATION_225517.00_false", "Details": {"Detail": [{"Type": "Cancel-Noshow", "Application": {"Code": "1"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 225517.0, "Code": "INR"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "false"}]}}, {"CurrencyAmountValue": {"value": 225517.0, "Code": "INR"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "false"}]}}]}}]}, "CancelFeeInd": false, "RefundableInd": false}, {"ObjectKey": "PEN_Cancellation_PDE_225517.00_NAV_N", "Details": {"Detail": [{"Type": "Cancel", "Application": {"Code": "2"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 225517.0, "Code": "INR"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "NAV"}]}}, {"CurrencyAmountValue": {"value": 225517.0, "Code": "INR"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "NAV"}]}}]}}]}, "CancelFeeInd": false, "RefundableInd": false}, {"ObjectKey": "PEN_Cancellation_ADE_225517.00_NAV_N", "Details": {"Detail": [{"Type": "Cancel", "Application": {"Code": "3"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 225517.0, "Code": "INR"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "NAV"}]}}, {"CurrencyAmountValue": {"value": 225517.0, "Code": "INR"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "NAV"}]}}]}}]}, "CancelFeeInd": false, "RefundableInd": false}, {"ObjectKey": "PEN_Change_PDE_200_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "2"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 200.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 200.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_ADE_200_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "3"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 200.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 200.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_PDE_150_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "2"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 150.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 150.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_ADE_150_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "3"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 150.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 150.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_PDE_20_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "2"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 20.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 20.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_ADE_20_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "3"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 20.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 20.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Cancellation_PDE_0_N", "Details": {"Detail": [{"Type": "Cancel", "Application": {"Code": "2"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 0, "Code": "INR"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 0, "Code": "INR"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "CancelFeeInd": false, "RefundableInd": true}, {"ObjectKey": "PEN_Cancellation_ADE_0_N", "Details": {"Detail": [{"Type": "Cancel", "Application": {"Code": "3"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 0, "Code": "INR"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 0, "Code": "INR"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "CancelFeeInd": false, "RefundableInd": true}, {"ObjectKey": "PEN_Change_PDE_0_N", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "2"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 0, "Code": "INR"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 0, "Code": "INR"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": false, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_ADE_0_N", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "3"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 0, "Code": "INR"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 0, "Code": "INR"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": false, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_PDE_350_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "2"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 350.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 350.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_ADE_350_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "3"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 350.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 350.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_PDE_263_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "2"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 263.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 263.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_ADE_263_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "3"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 263.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 263.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_PDE_35_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "2"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 35.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 35.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_ADE_35_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "3"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 35.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 35.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_PDE_250_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "2"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 250.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 250.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_ADE_250_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "3"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 250.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 250.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_PDE_188_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "2"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 188.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 188.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_ADE_188_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "3"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 188.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 188.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_PDE_25_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "2"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 25.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 25.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_ADE_25_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "3"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 25.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 25.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_PDE_300_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "2"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 300.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 300.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_ADE_300_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "3"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 300.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 300.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_PDE_225_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "2"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 225.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 225.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_ADE_225_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "3"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 225.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 225.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_PDE_30_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "2"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 30.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 30.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_Change_ADE_30_Y", "Details": {"Detail": [{"Type": "Change", "Application": {"Code": "3"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 30.0, "Code": "USD"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}, {"CurrencyAmountValue": {"value": 30.0, "Code": "USD"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "Allowed"}]}}]}}]}, "ChangeFeeInd": true, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_NoShow_NOSHOW_CHANGE_0_false", "Details": {"Detail": [{"Type": "Change-Noshow", "Application": {"Code": "1"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 0, "Code": "INR"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "true"}]}}, {"CurrencyAmountValue": {"value": 0, "Code": "INR"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "true"}]}}]}}]}, "ChangeFeeInd": false, "ChangeAllowedInd": true}, {"ObjectKey": "PEN_NoShow_NOSHOW_CANCELLATION_0_false", "Details": {"Detail": [{"Type": "Cancel-Noshow", "Application": {"Code": "1"}, "Amounts": {"Amount": [{"CurrencyAmountValue": {"value": 0, "Code": "INR"}, "AmountApplication": "MIN", "ApplicableFeeRemarks": {"Remark": [{"value": "true"}]}}, {"CurrencyAmountValue": {"value": 0, "Code": "INR"}, "AmountApplication": "MAX", "ApplicableFeeRemarks": {"Remark": [{"value": "true"}]}}]}}]}, "CancelFeeInd": false, "RefundableInd": true}]}, "PriceClassList": {"PriceClass": [{"ObjectKey": "PC1", "Name": "Light"}, {"ObjectKey": "PC2", "Name": "Standard"}, {"ObjectKey": "PC3", "Name": "Flex", "Descriptions": {"Description": [{"Text": {"value": "FREE_SEAT_INCLUDED"}}]}}, {"ObjectKey": "PC4", "Name": "Premium Light"}, {"ObjectKey": "PC5", "Name": "Premium Standard"}, {"ObjectKey": "PC6", "Name": "Premium Flex", "Descriptions": {"Description": [{"Text": {"value": "FREE_SEAT_INCLUDED"}}]}}, {"ObjectKey": "PC7", "Name": "Business Standard"}, {"ObjectKey": "PC8", "Name": "Business Flex", "Descriptions": {"Description": [{"Text": {"value": "FREE_SEAT_INCLUDED"}}]}}]}, "ServiceList": {"Service": [{"ServiceID": {"value": "SRVID1_SKY", "Owner": "AF"}, "Name": {"value": "SkyPriority"}, "Descriptions": {"Description": [{"Text": {"value": "SkyPriority"}}]}}, {"ServiceID": {"value": "SRVID2_LOUNGE_ACCESS", "Owner": "AF"}, "Name": {"value": "Lounge Access"}, "Descriptions": {"Description": [{"Text": {"value": "Lounge Access"}}]}}, {"ServiceID": {"value": "SRV-SEAT", "Owner": "AF"}, "Name": {"value": "FREE_SEAT"}, "Descriptions": {"Description": [{"Text": {"value": "FREE_SEAT_INCLUDED"}}]}}]}}}