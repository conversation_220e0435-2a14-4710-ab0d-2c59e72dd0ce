generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Booking {
  id                  Int       @id @default(autoincrement())
  userId              String    @map("user_id")
  bookingReference    String    @unique @map("booking_reference")
  orderItemId         String?   @map("order_item_id")
  airlineCode         String?   @map("airline_code")
  flightNumbers       String[]  @map("flight_numbers")
  routeSegments       Json?     @map("route_segments")
  passengerTypes      String[]  @map("passenger_types")
  documentNumbers     String[]  @map("document_numbers")
  classOfService      String?   @map("class_of_service")
  cabinClass          String?   @map("cabin_class")
  flightDetails       Json      @map("flight_details")
  passengerDetails    Json      @map("passenger_details")
  contactInfo         Json      @map("contact_info")
  extras              Json?
  totalAmount         Decimal   @map("total_amount") @db.Decimal(10, 2)
  status              String    @default("pending")
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")
  orderCreateResponse Json?     @map("order_create_response")
  originalFlightOffer Json?     @map("original_flight_offer")
  payments            Payment[]

  @@map("bookings")
}

model Payment {
  id              Int      @id @default(autoincrement())
  bookingId       Int      @map("booking_id")
  paymentIntentId String   @unique @map("payment_intent_id")
  amount          Decimal  @db.Decimal(10, 2)
  currency        String   @default("usd")
  status          String
  paymentMethod   String?  @map("payment_method")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  booking         Booking  @relation(fields: [bookingId], references: [id])

  @@map("payments")
}
